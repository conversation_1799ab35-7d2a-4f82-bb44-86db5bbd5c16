#!/usr/bin/env python3
"""
三个月SCI论文实验计划
基于AF-CLIP的异常检测改进
"""

# 实验1：基线对比（1周）
baseline_experiments = {
    "AF-CLIP": "现有方法",
    "CLIP-AD": "对比方法1", 
    "WinCLIP": "对比方法2",
    "AnomalyCLIP": "对比方法3"
}

# 实验2：消融研究（1周）
ablation_studies = {
    "prompt_length": [4, 8, 12, 16, 20],
    "feature_layers": [
        [6, 12, 18, 24],      # 原始
        [3, 6, 9, 12, 15, 18, 21, 24],  # 密集
        [12, 18, 24],         # 高层
        [6, 12, 18]           # 中层
    ],
    "lambda_values": {
        "lambda1": [0.5, 1.0, 2.0],
        "lambda2": [0.5, 1.0, 2.0]
    }
}

# 实验3：创新点验证（2周）
innovation_experiments = {
    "multi_scale_fusion": "多尺度特征融合",
    "adaptive_prompting": "自适应提示学习", 
    "cross_domain": "跨域泛化",
    "attention_mechanism": "注意力机制改进"
}

# 数据集评估计划
datasets = {
    "MVTec": "工业异常检测",
    "VISA": "视觉异常检测", 
    "BTAD": "纹理异常检测",
    "DAGM": "表面缺陷检测"
}

# 评估指标
metrics = [
    "Sample_AUROC", "Sample_AP", "Sample_F1",
    "Pixel_AUROC", "Pixel_AP", "Pixel_F1", 
    "Pixel_PRO", "FPS"
]

print("实验计划制定完成！")
