#!/bin/bash

# 设置数据目录路径 - 请根据你的实际数据路径修改
data_dir="/kaggle/working/AF-CLIP/data"  # 修改为你的数据目录路径

# 设置GPU设备
export CUDA_VISIBLE_DEVICES=0

echo "=== 开始使用MVTec数据集训练模型，并在VISA数据集上测试 ==="
echo "数据目录: $data_dir"
echo "训练数据集: MVTec"
echo "测试数据集: VISA"
echo ""

# 检查数据目录是否存在
if [ ! -d "$data_dir" ]; then
    echo "错误: 数据目录 $data_dir 不存在!"
    echo "请修改 data_dir 变量为正确的数据路径"
    exit 1
fi

# 检查MVTec数据集
if [ ! -d "$data_dir/mvtec" ]; then
    echo "警告: MVTec数据集目录 $data_dir/mvtec 不存在!"
fi

# 检查VISA数据集
if [ ! -d "$data_dir/visa" ]; then
    echo "警告: VISA数据集目录 $data_dir/visa 不存在!"
fi

# 创建日志目录
mkdir -p ./train_log

python main.py \
    --log_dir ./train_log \
    --dataset mvtec \
    --data_dir $data_dir \
    --test_dataset visa \
    --epochs 10 \
    --batch_size 8 \
    --lr 0.0001 \
    --lambda1 1.0 \
    --lambda2 1.0 \
    --img_size 518 \
    --prompt_len 12 \
    --seed 122

echo ""
echo "=== 训练和测试完成 ==="
echo "=== 结果已保存到 train_log 目录 ==="
echo "=== 查看日志文件了解详细结果 ==="
