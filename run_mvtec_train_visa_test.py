#!/usr/bin/env python3
"""
完整的MVTec训练+VISA测试流程
"""
import os
import subprocess
import sys

def run_command(cmd, description):
    """运行命令并打印输出"""
    print(f"\n{'='*60}")
    print(f"{description}")
    print(f"{'='*60}")
    print(f"执行命令: {cmd}")
    
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.stdout:
        print("输出:")
        print(result.stdout)
    
    if result.stderr:
        print("错误信息:")
        print(result.stderr)
    
    if result.returncode != 0:
        print(f"命令执行失败，返回码: {result.returncode}")
        return False
    
    print("命令执行成功!")
    return True

def main():
    # 配置参数
    data_dir = "./data"  # 请修改为你的数据目录路径
    gpu_id = "0"
    
    # 设置环境变量
    os.environ["CUDA_VISIBLE_DEVICES"] = gpu_id
    
    print("AF-CLIP: MVTec训练 + VISA测试")
    print(f"数据目录: {data_dir}")
    print(f"使用GPU: {gpu_id}")
    
    # 检查数据目录
    if not os.path.exists(data_dir):
        print(f"错误: 数据目录 {data_dir} 不存在!")
        print("请修改 data_dir 变量为正确的数据路径")
        return
    
    # 检查MVTec和VISA数据集
    mvtec_path = os.path.join(data_dir, "mvtec")
    visa_path = os.path.join(data_dir, "visa")
    
    if not os.path.exists(mvtec_path):
        print(f"警告: MVTec数据集路径 {mvtec_path} 不存在!")
    
    if not os.path.exists(visa_path):
        print(f"警告: VISA数据集路径 {visa_path} 不存在!")
    
    # 创建日志目录
    os.makedirs("./train_log", exist_ok=True)
    
    # 步骤1: 使用MVTec训练模型
    train_cmd = f"""python main.py \
        --log_dir ./train_log \
        --dataset mvtec \
        --data_dir {data_dir} \
        --test_dataset visa \
        --epochs 10 \
        --batch_size 8 \
        --lr 0.0001 \
        --lambda1 1.0 \
        --lambda2 1.0 \
        --img_size 518 \
        --prompt_len 12 \
        --seed 122"""
    
    success = run_command(train_cmd, "步骤1: 使用MVTec数据集训练模型并在VISA上测试")
    
    if success:
        print(f"\n{'='*60}")
        print("训练和测试完成!")
        print("结果文件位置:")
        print("- 训练日志: ./train_log/")
        print("- 模型权重: 自动保存在模型内部")
        print(f"{'='*60}")
    else:
        print("训练失败，请检查错误信息")

if __name__ == "__main__":
    main()
