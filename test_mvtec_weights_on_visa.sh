#!/bin/bash

# 设置数据目录路径 - 请根据你的实际数据路径修改
data_dir="./data"  # 修改为你的数据目录路径

# 设置GPU设备
export CUDA_VISIBLE_DEVICES=0

echo "=== 使用已有的MVTec预训练权重在VISA数据集上测试 ==="
python main.py \
    --log_dir ./test_log \
    --dataset mvtec \
    --data_dir $data_dir \
    --test_dataset visa \
    --weight ./weight \
    --batch_size 8 \
    --img_size 518 \
    --prompt_len 12 \
    --seed 122

echo "=== 测试完成，结果已保存到 test_log 目录 ==="
