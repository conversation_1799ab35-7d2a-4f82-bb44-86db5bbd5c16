"""
创新点2: 对比学习增强的Patch对齐
Contrastive Patch Alignment (CPA)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class ContrastivePatchAlignment(nn.Module):
    """
    对比学习增强的patch对齐损失
    """
    def __init__(self, temperature=0.07, margin=0.5):
        super().__init__()
        self.temperature = temperature
        self.margin = margin
        
    def forward(self, img_tokens, labels, gts, return_details=False):
        """
        Args:
            img_tokens: list of [B, N, D] 多层特征
            labels: [B] 图像级标签
            gts: [B, H, W] 像素级标签
        """
        total_loss = 0
        loss_details = {}
        
        for layer_idx, img_token in enumerate(img_tokens):
            # 去除class token
            patch_features = img_token[:, 1:, :]  # [B, N-1, D]
            B, N, D = patch_features.shape
            
            # 重塑gt到patch尺寸
            H = W = int(N ** 0.5)
            gts_resized = F.interpolate(
                gts.unsqueeze(1).float(), 
                size=(H, W), 
                mode='bilinear'
            ).squeeze(1)  # [B, H, W]
            gts_flat = gts_resized.view(B, -1)  # [B, N]
            
            # 标准化特征
            patch_features_norm = F.normalize(patch_features, dim=-1)
            
            # 计算patch间相似度矩阵
            similarity_matrix = torch.bmm(
                patch_features_norm, 
                patch_features_norm.transpose(1, 2)
            ) / self.temperature  # [B, N, N]
            
            # 构建正负样本对
            positive_pairs, negative_pairs = self._construct_pairs(
                gts_flat, labels
            )
            
            # 计算对比损失
            contrastive_loss = self._compute_contrastive_loss(
                similarity_matrix, positive_pairs, negative_pairs
            )
            
            # 计算一致性损失（原始patch alignment的改进版）
            consistency_loss = self._compute_consistency_loss(
                patch_features_norm, gts_flat
            )
            
            layer_loss = contrastive_loss + 0.5 * consistency_loss
            total_loss += layer_loss
            
            if return_details:
                loss_details[f'layer_{layer_idx}'] = {
                    'contrastive': contrastive_loss.item(),
                    'consistency': consistency_loss.item()
                }
        
        avg_loss = total_loss / len(img_tokens)
        
        if return_details:
            return avg_loss, loss_details
        return avg_loss
    
    def _construct_pairs(self, gts_flat, labels):
        """构建正负样本对"""
        B, N = gts_flat.shape
        positive_pairs = []
        negative_pairs = []
        
        for b in range(B):
            gt = gts_flat[b]  # [N]
            label = labels[b]
            
            if label == 0:  # 正常样本
                # 所有patch都是正样本对
                normal_indices = torch.arange(N, device=gt.device)
                pos_pairs = torch.combinations(normal_indices, 2)
                positive_pairs.append((b, pos_pairs))
            else:  # 异常样本
                # 异常patch之间为正样本对
                abnormal_indices = torch.where(gt > 0.5)[0]
                normal_indices = torch.where(gt <= 0.5)[0]
                
                if len(abnormal_indices) > 1:
                    pos_pairs = torch.combinations(abnormal_indices, 2)
                    positive_pairs.append((b, pos_pairs))
                
                # 异常patch与正常patch为负样本对
                if len(abnormal_indices) > 0 and len(normal_indices) > 0:
                    neg_pairs = torch.cartesian_prod(abnormal_indices, normal_indices)
                    negative_pairs.append((b, neg_pairs))
        
        return positive_pairs, negative_pairs
    
    def _compute_contrastive_loss(self, similarity_matrix, positive_pairs, negative_pairs):
        """计算对比损失"""
        pos_loss = 0
        neg_loss = 0
        
        # 正样本对损失
        for batch_idx, pairs in positive_pairs:
            if len(pairs) > 0:
                sim_values = similarity_matrix[batch_idx, pairs[:, 0], pairs[:, 1]]
                pos_loss += -torch.log(torch.sigmoid(sim_values)).mean()
        
        # 负样本对损失  
        for batch_idx, pairs in negative_pairs:
            if len(pairs) > 0:
                sim_values = similarity_matrix[batch_idx, pairs[:, 0], pairs[:, 1]]
                neg_loss += -torch.log(torch.sigmoid(-sim_values + self.margin)).mean()
        
        total_pairs = len(positive_pairs) + len(negative_pairs)
        if total_pairs > 0:
            return (pos_loss + neg_loss) / total_pairs
        else:
            return torch.tensor(0.0, device=similarity_matrix.device)
    
    def _compute_consistency_loss(self, patch_features_norm, gts_flat):
        """计算一致性损失"""
        B, N, D = patch_features_norm.shape
        
        consistency_loss = 0
        for b in range(B):
            features = patch_features_norm[b]  # [N, D]
            gt = gts_flat[b]  # [N]
            
            # 计算异常patch的平均特征
            abnormal_mask = gt > 0.5
            normal_mask = gt <= 0.5
            
            if abnormal_mask.sum() > 0 and normal_mask.sum() > 0:
                abnormal_feat = features[abnormal_mask].mean(dim=0)
                normal_feat = features[normal_mask].mean(dim=0)
                
                # 最大化异常和正常特征的距离
                distance = F.cosine_similarity(
                    abnormal_feat.unsqueeze(0), 
                    normal_feat.unsqueeze(0)
                )
                consistency_loss += torch.relu(distance + 0.1)  # margin=0.1
        
        return consistency_loss / B

# 集成到主训练循环
def improved_patch_alignment_loss(img_tokens, labels, gts):
    """
    替换原始的patch_alignment_loss函数
    """
    cpa_module = ContrastivePatchAlignment(temperature=0.07, margin=0.5)
    return cpa_module(img_tokens, labels, gts)

if __name__ == "__main__":
    print("创新点2: 对比学习增强的Patch对齐")
    print("优势: 1) 引入对比学习，增强特征判别性")
    print("     2) 改进原始patch alignment loss")
    print("     3) 理论基础扎实，容易写论文")
