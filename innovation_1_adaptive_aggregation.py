"""
创新点1: 自适应多层级空间聚合
Multi-Level Adaptive Spatial Aggregation (MASA)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class AdaptiveSpatialAggregation(nn.Module):
    """
    自适应空间聚合模块
    根据图像内容动态调整聚合范围
    """
    def __init__(self, feature_dim=768, num_scales=5):
        super().__init__()
        self.num_scales = num_scales
        self.feature_dim = feature_dim
        
        # 学习每个尺度的重要性权重
        self.scale_attention = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 4),
            nn.ReLU(),
            nn.Linear(feature_dim // 4, num_scales),
            nn.Softmax(dim=-1)
        )
        
        # 可学习的聚合半径
        self.radius_predictor = nn.Sequential(
            nn.Linear(feature_dim, 32),
            nn.ReLU(),
            nn.Linear(32, num_scales),
            nn.Sigmoid()  # 输出0-1，后续映射到实际半径
        )
        
    def forward(self, patch_features, spatial_size):
        """
        Args:
            patch_features: [B, N, D] patch特征
            spatial_size: (H, W) 空间尺寸
        """
        B, N, D = patch_features.shape
        H, W = spatial_size
        
        # 计算全局特征用于预测权重和半径
        global_feat = patch_features.mean(dim=1)  # [B, D]
        
        # 预测每个尺度的注意力权重
        scale_weights = self.scale_attention(global_feat)  # [B, num_scales]
        
        # 预测自适应半径 (映射到1-7的范围)
        adaptive_radius = self.radius_predictor(global_feat) * 6 + 1  # [B, num_scales]
        
        # 重塑patch特征到空间维度
        patch_features_2d = patch_features.view(B, H, W, D)
        
        aggregated_features = []
        
        for scale_idx in range(self.num_scales):
            # 获取当前尺度的半径
            current_radius = adaptive_radius[:, scale_idx].mean().item()
            current_radius = int(round(current_radius))
            
            # 执行空间聚合
            if current_radius == 1:
                # 无聚合
                agg_feat = patch_features_2d
            else:
                # 使用平均池化进行聚合
                kernel_size = min(current_radius * 2 + 1, min(H, W))
                padding = kernel_size // 2
                
                # 转换为 [B, D, H, W] 格式进行池化
                feat_for_pool = patch_features_2d.permute(0, 3, 1, 2)
                agg_feat = F.avg_pool2d(feat_for_pool, kernel_size, stride=1, padding=padding)
                agg_feat = agg_feat.permute(0, 2, 3, 1)  # 转回 [B, H, W, D]
            
            aggregated_features.append(agg_feat)
        
        # 加权融合不同尺度的特征
        final_features = torch.zeros_like(aggregated_features[0])
        for scale_idx, feat in enumerate(aggregated_features):
            weight = scale_weights[:, scale_idx].view(B, 1, 1, 1)
            final_features += weight * feat
        
        return final_features.view(B, N, D)

# 使用示例
def integrate_adaptive_aggregation():
    """
    如何集成到AF-CLIP中
    """
    code_modification = """
    # 在clip/adaptor.py中添加：
    
    class ImprovedAdaptor(nn.Module):
        def __init__(self, ...):
            super().__init__()
            # 原有代码...
            
            # 添加自适应聚合模块
            self.adaptive_aggregation = AdaptiveSpatialAggregation(
                feature_dim=768, 
                num_scales=5
            )
        
        def forward(self, image_features):
            # 原有的特征处理...
            
            # 应用自适应聚合
            B, N, D = image_features.shape
            H = W = int((N - 1) ** 0.5)  # 假设是方形patch
            
            aggregated_features = self.adaptive_aggregation(
                image_features[:, 1:, :],  # 去除class token
                (H, W)
            )
            
            # 重新添加class token
            final_features = torch.cat([
                image_features[:, :1, :],  # class token
                aggregated_features
            ], dim=1)
            
            return final_features
    """
    return code_modification

if __name__ == "__main__":
    print("创新点1: 自适应多层级空间聚合")
    print("优势: 1) 根据图像内容自适应调整聚合策略")
    print("     2) 可学习的多尺度融合权重") 
    print("     3) 实现相对简单，容易验证效果")
