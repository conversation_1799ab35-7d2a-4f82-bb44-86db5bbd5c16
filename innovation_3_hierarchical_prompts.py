"""
创新点3: 层次化Prompt学习
Hierarchical Prompt Learning (HPL)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class HierarchicalPromptLearning(nn.Module):
    """
    层次化prompt学习模块
    为不同的Transformer层设计不同的prompt
    """
    def __init__(self, 
                 num_layers=24, 
                 prompt_length=12, 
                 embed_dim=768,
                 num_prompt_types=3):
        super().__init__()
        self.num_layers = num_layers
        self.prompt_length = prompt_length
        self.embed_dim = embed_dim
        self.num_prompt_types = num_prompt_types
        
        # 定义三种类型的prompt
        # 1. 低级特征prompt (纹理、边缘等)
        self.low_level_prompts = nn.Parameter(
            torch.randn(prompt_length, embed_dim) * 0.02
        )
        
        # 2. 中级特征prompt (形状、结构等)  
        self.mid_level_prompts = nn.Parameter(
            torch.randn(prompt_length, embed_dim) * 0.02
        )
        
        # 3. 高级特征prompt (语义、上下文等)
        self.high_level_prompts = nn.Parameter(
            torch.randn(prompt_length, embed_dim) * 0.02
        )
        
        # 层级分配策略
        self.layer_to_prompt_type = self._assign_layers_to_prompts()
        
        # 自适应融合权重
        self.fusion_weights = nn.Parameter(
            torch.ones(num_layers, num_prompt_types) / num_prompt_types
        )
        
        # prompt增强模块
        self.prompt_enhancer = nn.ModuleList([
            nn.Sequential(
                nn.Linear(embed_dim, embed_dim),
                nn.GELU(),
                nn.Linear(embed_dim, embed_dim),
                nn.LayerNorm(embed_dim)
            ) for _ in range(num_prompt_types)
        ])
        
    def _assign_layers_to_prompts(self):
        """为每一层分配主要的prompt类型"""
        layer_assignment = {}
        
        # 低级特征层 (前1/3)
        low_layers = list(range(0, self.num_layers // 3))
        
        # 中级特征层 (中1/3)  
        mid_layers = list(range(self.num_layers // 3, 2 * self.num_layers // 3))
        
        # 高级特征层 (后1/3)
        high_layers = list(range(2 * self.num_layers // 3, self.num_layers))
        
        for layer in range(self.num_layers):
            if layer in low_layers:
                layer_assignment[layer] = 0  # 低级
            elif layer in mid_layers:
                layer_assignment[layer] = 1  # 中级
            else:
                layer_assignment[layer] = 2  # 高级
                
        return layer_assignment
    
    def get_layer_prompts(self, layer_idx, batch_size):
        """获取指定层的prompt"""
        # 获取三种类型的prompt
        prompts = [
            self.low_level_prompts,
            self.mid_level_prompts, 
            self.high_level_prompts
        ]
        
        # 应用增强
        enhanced_prompts = []
        for i, prompt in enumerate(prompts):
            enhanced = self.prompt_enhancer[i](prompt)
            enhanced_prompts.append(enhanced)
        
        # 获取当前层的融合权重
        weights = F.softmax(self.fusion_weights[layer_idx], dim=0)
        
        # 加权融合
        fused_prompt = torch.zeros_like(enhanced_prompts[0])
        for i, prompt in enumerate(enhanced_prompts):
            fused_prompt += weights[i] * prompt
        
        # 扩展到batch维度
        batch_prompts = fused_prompt.unsqueeze(0).expand(batch_size, -1, -1)
        
        return batch_prompts
    
    def forward(self, layer_idx, batch_size):
        """前向传播"""
        return self.get_layer_prompts(layer_idx, batch_size)

class AdaptivePromptSelector(nn.Module):
    """
    自适应prompt选择器
    根据输入图像特征动态选择最适合的prompt组合
    """
    def __init__(self, embed_dim=768, num_prompt_types=3):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_prompt_types = num_prompt_types
        
        # 特征分析器
        self.feature_analyzer = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.GELU(),
            nn.Linear(embed_dim // 2, embed_dim // 4),
            nn.GELU(),
            nn.Linear(embed_dim // 4, num_prompt_types),
            nn.Softmax(dim=-1)
        )
        
    def forward(self, image_features):
        """
        Args:
            image_features: [B, N, D] 图像特征
        Returns:
            prompt_weights: [B, num_prompt_types] 每种prompt的权重
        """
        # 使用class token作为全局特征
        global_features = image_features[:, 0, :]  # [B, D]
        
        # 预测prompt权重
        prompt_weights = self.feature_analyzer(global_features)
        
        return prompt_weights

# 集成示例
def integrate_hierarchical_prompts():
    """
    如何集成到AF-CLIP中
    """
    integration_code = """
    # 在clip/model.py中修改CLIP类
    
    class CLIP(nn.Module):
        def __init__(self, ...):
            super().__init__()
            # 原有初始化...
            
            # 添加层次化prompt学习
            self.hierarchical_prompts = HierarchicalPromptLearning(
                num_layers=24,
                prompt_length=12,
                embed_dim=768
            )
            
            # 添加自适应选择器
            self.prompt_selector = AdaptivePromptSelector(
                embed_dim=768,
                num_prompt_types=3
            )
        
        def encode_image(self, image):
            # 获取初始特征
            x = self.visual.conv1(image)
            # ... 其他预处理
            
            # 在每一层中使用层次化prompt
            for i, layer in enumerate(self.visual.transformer.resblocks):
                # 获取当前层的prompt
                layer_prompts = self.hierarchical_prompts(i, x.size(0))
                
                # 将prompt添加到输入中
                x_with_prompt = torch.cat([x, layer_prompts], dim=1)
                
                # 通过transformer层
                x = layer(x_with_prompt)
                
                # 移除prompt部分，保留原始特征
                x = x[:, :-self.hierarchical_prompts.prompt_length, :]
            
            return x
    """
    return integration_code

if __name__ == "__main__":
    print("创新点3: 层次化Prompt学习")
    print("优势: 1) 不同层使用不同类型的prompt")
    print("     2) 自适应融合多种prompt类型")
    print("     3) 理论新颖，容易产生高影响因子论文")
